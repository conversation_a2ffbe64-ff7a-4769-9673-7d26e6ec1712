# MCP Feedback Enhanced 自动化测试脚本 (优化版)
# 适用于 Windows PowerShell 环境

param(
    [switch]$SkipInstall,
    [switch]$DebugMode,
    [switch]$QuickTest
)

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 测试结果记录
$TestResults = @()

function Record-TestResult {
    param(
        [string]$TestName,
        [string]$Status,
        [string]$Details = ""
    )
    $TestResults += [PSCustomObject]@{
        Test = $TestName
        Status = $Status
        Details = $Details
        Timestamp = Get-Date -Format "HH:mm:ss"
    }
    
    $color = switch ($Status) {
        "成功" { "Green" }
        "失败" { "Red" }
        "警告" { "Yellow" }
        "跳过" { "Cyan" }
        default { "White" }
    }
    Write-ColorOutput "  [$($Status)] $TestName $(if($Details) { "- $Details" })" $color
}

# 主测试函数
function Start-MCPTest {
    Write-ColorOutput "`n🚀 MCP Feedback Enhanced 自动化测试开始" "Green"
    Write-ColorOutput "=" * 50 "Green"
    Write-ColorOutput "测试时间: $(Get-Date)" "Gray"
    Write-ColorOutput "工作目录: $(Get-Location)" "Gray"
    Write-ColorOutput ""

    # 阶段1: 环境检查
    Write-ColorOutput "📋 阶段1: 基础环境检查" "Yellow"
    Test-Environment

    # 阶段2: Python和包管理器检查
    Write-ColorOutput "`n📋 阶段2: Python环境检查" "Yellow"
    Test-PythonEnvironment

    # 阶段3: 安装必要工具
    if (-not $SkipInstall) {
        Write-ColorOutput "`n📋 阶段3: 安装必要工具" "Yellow"
        Install-RequiredTools
    }

    # 阶段4: MCP测试
    Write-ColorOutput "`n📋 阶段4: MCP Feedback Enhanced 测试" "Yellow"
    Test-MCPFeedbackEnhanced

    # 输出测试结果
    Show-TestSummary
}

function Test-Environment {
    # 检查PowerShell版本
    $psVersion = $PSVersionTable.PSVersion
    if ($psVersion.Major -ge 5) {
        Record-TestResult "PowerShell版本" "成功" "v$($psVersion.Major).$($psVersion.Minor)"
    } else {
        Record-TestResult "PowerShell版本" "警告" "版本较低: v$($psVersion.Major).$($psVersion.Minor)"
    }

    # 检查网络连接
    try {
        $null = Test-NetConnection -ComputerName "pypi.org" -Port 443 -InformationLevel Quiet -WarningAction SilentlyContinue
        Record-TestResult "网络连接" "成功" "可访问 PyPI"
    } catch {
        Record-TestResult "网络连接" "警告" "无法测试网络连接"
    }

    # 检查执行策略
    $policy = Get-ExecutionPolicy
    if ($policy -eq "Restricted") {
        Record-TestResult "执行策略" "警告" "当前为 Restricted，可能影响脚本执行"
    } else {
        Record-TestResult "执行策略" "成功" $policy
    }
}

function Test-PythonEnvironment {
    # 检查Python
    try {
        $pythonVersion = python --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Record-TestResult "Python" "成功" $pythonVersion
            
            # 检查pip
            try {
                $pipVersion = python -m pip --version 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Record-TestResult "pip" "成功" $pipVersion.Split()[1]
                } else {
                    Record-TestResult "pip" "失败" "pip不可用"
                }
            } catch {
                Record-TestResult "pip" "失败" "无法检查pip"
            }
        } else {
            Record-TestResult "Python" "失败" "Python未安装或不在PATH中"
        }
    } catch {
        Record-TestResult "Python" "失败" "无法执行python命令"
    }

    # 检查uv
    try {
        $uvVersion = uv --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Record-TestResult "uv工具" "成功" $uvVersion
        } else {
            Record-TestResult "uv工具" "失败" "uv未安装"
        }
    } catch {
        Record-TestResult "uv工具" "失败" "无法执行uv命令"
    }

    # 检查uvx
    try {
        $uvxHelp = uvx --help 2>&1
        if ($LASTEXITCODE -eq 0) {
            Record-TestResult "uvx工具" "成功" "可用"
        } else {
            Record-TestResult "uvx工具" "失败" "uvx不可用"
        }
    } catch {
        Record-TestResult "uvx工具" "失败" "无法执行uvx命令"
    }
}

function Install-RequiredTools {
    # 安装uv (如果Python可用但uv不可用)
    $pythonAvailable = $TestResults | Where-Object { $_.Test -eq "Python" -and $_.Status -eq "成功" }
    $uvAvailable = $TestResults | Where-Object { $_.Test -eq "uv工具" -and $_.Status -eq "成功" }
    
    if ($pythonAvailable -and -not $uvAvailable) {
        Write-ColorOutput "  正在安装 uv..." "Cyan"
        try {
            python -m pip install uv
            if ($LASTEXITCODE -eq 0) {
                Record-TestResult "安装uv" "成功" "通过pip安装"
            } else {
                Record-TestResult "安装uv" "失败" "pip安装失败"
            }
        } catch {
            Record-TestResult "安装uv" "失败" "安装过程出错"
        }
    } elseif ($uvAvailable) {
        Record-TestResult "安装uv" "跳过" "uv已可用"
    } else {
        Record-TestResult "安装uv" "跳过" "Python不可用，无法安装"
    }
}

function Test-MCPFeedbackEnhanced {
    # 检查uvx是否可用
    $uvxAvailable = $TestResults | Where-Object { $_.Test -eq "uvx工具" -and $_.Status -eq "成功" }
    
    if (-not $uvxAvailable) {
        Record-TestResult "MCP版本检查" "跳过" "uvx不可用"
        Record-TestResult "MCP功能测试" "跳过" "uvx不可用"
        return
    }

    # 测试1: 版本检查
    Write-ColorOutput "  正在检查 MCP Feedback Enhanced 版本..." "Cyan"
    try {
        $versionOutput = uvx mcp-feedback-enhanced@latest version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Record-TestResult "MCP版本检查" "成功" $versionOutput
        } else {
            Record-TestResult "MCP版本检查" "失败" $versionOutput
        }
    } catch {
        Record-TestResult "MCP版本检查" "失败" "命令执行出错"
    }

    # 测试2: 调试模式版本检查
    if ($DebugMode) {
        Write-ColorOutput "  正在进行调试模式测试..." "Cyan"
        try {
            $env:MCP_DEBUG = "true"
            $debugOutput = uvx mcp-feedback-enhanced@latest version 2>&1
            if ($LASTEXITCODE -eq 0) {
                Record-TestResult "MCP调试模式" "成功" "调试信息已输出"
            } else {
                Record-TestResult "MCP调试模式" "失败" $debugOutput
            }
            Remove-Item env:MCP_DEBUG -ErrorAction SilentlyContinue
        } catch {
            Record-TestResult "MCP调试模式" "失败" "调试模式测试出错"
        }
    }

    # 测试3: 快速功能测试 (如果不是快速测试模式)
    if (-not $QuickTest) {
        $testChoice = Read-Host "`n  是否进行Web UI功能测试？这将启动Web服务器 (y/n)"
        if ($testChoice -eq "y" -or $testChoice -eq "Y") {
            Write-ColorOutput "  正在进行Web UI测试 (10秒超时)..." "Cyan"
            try {
                $job = Start-Job -ScriptBlock { uvx mcp-feedback-enhanced@latest test --web }
                $null = Wait-Job $job -Timeout 10
                Stop-Job $job -ErrorAction SilentlyContinue
                Remove-Job $job -ErrorAction SilentlyContinue
                Record-TestResult "MCP Web UI测试" "成功" "测试已执行"
            } catch {
                Record-TestResult "MCP Web UI测试" "失败" "测试执行出错"
            }
        } else {
            Record-TestResult "MCP Web UI测试" "跳过" "用户选择跳过"
        }
    }
}

function Show-TestSummary {
    Write-ColorOutput "`n📊 测试结果汇总" "Green"
    Write-ColorOutput "=" * 50 "Green"
    
    $TestResults | Format-Table -AutoSize
    
    $successCount = ($TestResults | Where-Object { $_.Status -eq "成功" }).Count
    $failCount = ($TestResults | Where-Object { $_.Status -eq "失败" }).Count
    $warnCount = ($TestResults | Where-Object { $_.Status -eq "警告" }).Count
    $skipCount = ($TestResults | Where-Object { $_.Status -eq "跳过" }).Count
    
    Write-ColorOutput "`n📈 统计信息:" "Yellow"
    Write-ColorOutput "  成功: $successCount" "Green"
    Write-ColorOutput "  失败: $failCount" "Red"
    Write-ColorOutput "  警告: $warnCount" "Yellow"
    Write-ColorOutput "  跳过: $skipCount" "Cyan"
    
    # 提供建议
    Write-ColorOutput "`n🎯 建议和下一步:" "Yellow"
    
    if ($failCount -eq 0) {
        Write-ColorOutput "  ✅ 所有关键测试都通过了！" "Green"
        Write-ColorOutput "  📝 如果MCP配置仍有问题，请检查配置文件格式" "Cyan"
    } else {
        Write-ColorOutput "  ❌ 发现 $failCount 个问题需要解决:" "Red"
        
        $failedTests = $TestResults | Where-Object { $_.Status -eq "失败" }
        foreach ($test in $failedTests) {
            switch ($test.Test) {
                "Python" { 
                    Write-ColorOutput "    • 安装Python: https://python.org/downloads" "White"
                }
                "uv工具" { 
                    Write-ColorOutput "    • 安装uv: pip install uv" "White"
                }
                "uvx工具" { 
                    Write-ColorOutput "    • uvx是uv的一部分，确保uv正确安装" "White"
                }
                "MCP版本检查" { 
                    Write-ColorOutput "    • 检查网络连接和防火墙设置" "White"
                    Write-ColorOutput "    • 尝试: uv cache clean" "White"
                }
            }
        }
    }
    
    Write-ColorOutput "`n🔧 如需更多帮助，请运行:" "Cyan"
    Write-ColorOutput "  .\mcp_test_script.ps1 -DebugMode" "White"
    Write-ColorOutput "  .\mcp_test_script.ps1 -QuickTest" "White"
}

# 脚本入口点
if ($MyInvocation.InvocationName -ne '.') {
    Start-MCPTest
}
