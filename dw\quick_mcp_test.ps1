# MCP Feedback Enhanced 快速诊断脚本
# 简化版本，专注于核心问题诊断

Write-Host "🚀 MCP Feedback Enhanced 快速诊断" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green
Write-Host ""

# 测试结果数组
$results = @()

# 辅助函数
function Test-Command {
    param([string]$Command, [string]$Name)
    try {
        $output = Invoke-Expression "$Command 2>&1"
        if ($LASTEXITCODE -eq 0 -or $output -match "version|help") {
            $script:results += "✅ $Name : 可用"
            Write-Host "✅ $Name : 可用" -ForegroundColor Green
            return $true
        } else {
            $script:results += "❌ $Name : 不可用 - $output"
            Write-Host "❌ $Name : 不可用" -ForegroundColor Red
            return $false
        }
    } catch {
        $script:results += "❌ $Name : 错误 - $($_.Exception.Message)"
        Write-Host "❌ $Name : 错误" -ForegroundColor Red
        return $false
    }
}

# 1. 基础环境检查
Write-Host "📋 步骤1: 基础环境检查" -ForegroundColor Yellow

# PowerShell版本
$psVersion = $PSVersionTable.PSVersion
Write-Host "  PowerShell版本: $($psVersion.Major).$($psVersion.Minor)" -ForegroundColor Cyan
$script:results += "ℹ️ PowerShell版本: $($psVersion.Major).$($psVersion.Minor)"

# 执行策略
$policy = Get-ExecutionPolicy
Write-Host "  执行策略: $policy" -ForegroundColor Cyan
$script:results += "ℹ️ 执行策略: $policy"

Write-Host ""

# 2. Python环境检查
Write-Host "📋 步骤2: Python环境检查" -ForegroundColor Yellow

$pythonOK = Test-Command "python --version" "Python"
if ($pythonOK) {
    Test-Command "python -m pip --version" "pip"
}

Write-Host ""

# 3. UV工具检查
Write-Host "📋 步骤3: UV工具检查" -ForegroundColor Yellow

$uvOK = Test-Command "uv --version" "uv"
if ($uvOK) {
    $uvxOK = Test-Command "uvx --help" "uvx"
} else {
    Write-Host "❌ uvx : 跳过 (uv不可用)" -ForegroundColor Red
    $script:results += "❌ uvx : 跳过 (uv不可用)"
    $uvxOK = $false
}

Write-Host ""

# 4. MCP Feedback Enhanced 测试
Write-Host "📋 步骤4: MCP Feedback Enhanced 测试" -ForegroundColor Yellow

if ($uvxOK) {
    Write-Host "  正在测试 MCP Feedback Enhanced..." -ForegroundColor Cyan
    
    # 版本检查
    try {
        $mcpOutput = uvx mcp-feedback-enhanced@latest version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ MCP版本检查: 成功" -ForegroundColor Green
            $results += "✅ MCP版本检查: 成功 - $mcpOutput"
        } else {
            Write-Host "❌ MCP版本检查: 失败" -ForegroundColor Red
            $results += "❌ MCP版本检查: 失败 - $mcpOutput"
        }
    } catch {
        Write-Host "❌ MCP版本检查: 错误" -ForegroundColor Red
        $results += "❌ MCP版本检查: 错误 - $($_.Exception.Message)"
    }
    
    # 缓存清理测试
    Write-Host "  正在测试缓存清理..." -ForegroundColor Cyan
    try {
        $cacheOutput = uv cache clean 2>&1
        Write-Host "✅ 缓存清理: 完成" -ForegroundColor Green
        $results += "✅ 缓存清理: 完成"
    } catch {
        Write-Host "❌ 缓存清理: 失败" -ForegroundColor Red
        $results += "❌ 缓存清理: 失败 - $($_.Exception.Message)"
    }
    
} else {
    Write-Host "❌ MCP测试: 跳过 (uvx不可用)" -ForegroundColor Red
    $results += "❌ MCP测试: 跳过 (uvx不可用)"
}

Write-Host ""

# 5. 结果汇总和建议
Write-Host "📊 诊断结果汇总" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green

foreach ($result in $results) {
    Write-Host $result
}

Write-Host ""
Write-Host "🎯 问题诊断和建议:" -ForegroundColor Yellow

# 分析结果并提供建议
$pythonMissing = $results -match "❌ Python"
$uvMissing = $results -match "❌ uv"
$mcpFailed = $results -match "❌ MCP"

if ($pythonMissing) {
    Write-Host "🔧 Python问题:" -ForegroundColor Red
    Write-Host "   1. 安装Python: https://python.org/downloads" -ForegroundColor White
    Write-Host "   2. 确保Python添加到PATH环境变量" -ForegroundColor White
    Write-Host "   3. 重启终端后重新测试" -ForegroundColor White
}

if ($uvMissing -and -not $pythonMissing) {
    Write-Host "🔧 UV工具问题:" -ForegroundColor Red
    Write-Host "   1. 安装uv: python -m pip install uv" -ForegroundColor White
    Write-Host "   2. 或使用官方安装器: https://docs.astral.sh/uv/getting-started/installation/" -ForegroundColor White
}

if ($mcpFailed -and -not $uvMissing) {
    Write-Host "🔧 MCP Feedback Enhanced问题:" -ForegroundColor Red
    Write-Host "   1. 检查网络连接" -ForegroundColor White
    Write-Host "   2. 尝试: uv cache clean" -ForegroundColor White
    Write-Host "   3. 检查防火墙设置" -ForegroundColor White
    Write-Host "   4. 尝试调试模式: `$env:MCP_DEBUG='true'; uvx mcp-feedback-enhanced@latest version" -ForegroundColor White
}

if (-not $pythonMissing -and -not $uvMissing -and -not $mcpFailed) {
    Write-Host "✅ 所有基础组件都正常!" -ForegroundColor Green
    Write-Host "🔧 如果MCP配置仍有问题，请检查:" -ForegroundColor Yellow
    Write-Host "   1. MCP配置文件的JSON格式" -ForegroundColor White
    Write-Host "   2. autoApprove设置: ['interactive_feedback']" -ForegroundColor White
    Write-Host "   3. timeout设置: 600" -ForegroundColor White
    Write-Host "   4. 尝试重启MCP客户端" -ForegroundColor White
}

Write-Host ""
Write-Host "📝 配置文件模板:" -ForegroundColor Cyan
Write-Host @"
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "autoApprove": ["interactive_feedback"]
    }
  }
}
"@ -ForegroundColor White

Write-Host ""
Write-Host "🔄 重新运行此脚本: .\quick_mcp_test.ps1" -ForegroundColor Cyan
Write-Host "📞 如需更多帮助，请提供上述诊断结果" -ForegroundColor Cyan
